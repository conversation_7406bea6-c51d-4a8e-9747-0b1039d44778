# 海外Docker镜像自动同步到腾讯云CNB平台

## 项目简介

这个项目可以自动将海外的 Docker 镜像同步到腾讯云 CNB 平台，解决国内访问海外镜像仓库速度慢的问题。

## 功能特点

- ✅ **批量同步**: 支持一次性同步多个镜像
- ✅ **自动创建仓库**: 自动在 CNB 平台创建对应的仓库
- ✅ **多种镜像格式支持**: 支持官方镜像、用户镜像、带标签镜像等
- ✅ **定时同步**: 支持定时检查和更新镜像
- ✅ **高效传输**: 使用 skopeo 工具，支持多架构镜像
- ✅ **错误处理**: 完善的错误处理和日志输出

## 使用方法

### 1. 配置环境变量

在 CNB 平台项目中设置以下环境变量：

- `token`: CNB 平台的访问令牌
- `CNB_GROUP_SLUG_LOWERCASE`: CNB 项目组的小写标识符

### 2. 配置要同步的镜像

编辑 `.cnb.yml` 文件中的 `images.txt` 部分，添加需要同步的镜像：

```bash
# 官方镜像
nginx
redis:7-alpine
mysql:8.0

# 用户镜像
whyour/qinglong:latest
portainer/portainer-ce

# 完整路径镜像
docker.io/library/ubuntu:22.04
registry.hub.docker.com/library/node:18-alpine
```

### 3. 运行同步

- **手动触发**: 在 CNB 平台手动运行流水线
- **定时同步**: 取消注释 `crontab` 行启用定时任务

## 镜像格式支持

### 支持的格式

1. **简单镜像名**: `nginx` → `docker.cnb.cool/你的组名/nginx`
2. **带标签**: `nginx:alpine` → `docker.cnb.cool/你的组名/nginx:alpine`
3. **用户/镜像**: `whyour/qinglong` → `docker.cnb.cool/你的组名/whyour/qinglong`
4. **完整路径**: `docker.io/library/nginx:latest` → `docker.cnb.cool/你的组名/nginx:latest`

### 转换规则

原镜像地址 | 转换后地址
---|---
`nginx` | `docker.cnb.cool/你的组名/nginx`
`nginx:alpine` | `docker.cnb.cool/你的组名/nginx:alpine`
`whyour/qinglong:latest` | `docker.cnb.cool/你的组名/whyour/qinglong:latest`
`docker.io/library/ubuntu:22.04` | `docker.cnb.cool/你的组名/ubuntu:22.04`

## 使用同步后的镜像

同步完成后，你可以使用以下命令拉取镜像：

```bash
# 原来的命令
docker pull whyour/qinglong:latest

# 现在使用CNB镜像
docker pull docker.cnb.cool/你的组名/whyour/qinglong:latest
```

## 定时同步设置

如需启用定时同步，修改 `.cnb.yml` 文件：

```yaml
# 取消注释这一行，启用每天凌晨2点同步
"crontab: 0 2 * * *":
```

## 常见问题

### Q: 如何添加新的镜像？
A: 编辑 `.cnb.yml` 文件中的 `images.txt` 部分，添加新的镜像地址，然后手动触发流水线。

### Q: 同步失败怎么办？
A: 查看流水线日志，常见原因：
- 网络连接问题
- 镜像不存在
- 权限不足

### Q: 如何批量替换项目中的镜像地址？
A: 可以使用以下脚本批量替换：

```bash
# 替换 docker-compose.yml 中的镜像地址
sed -i 's|whyour/qinglong:latest|docker.cnb.cool/你的组名/whyour/qinglong:latest|g' docker-compose.yml
```

## 注意事项

1. **存储空间**: 确保 CNB 项目有足够的存储空间
2. **网络环境**: 首次同步需要良好的网络环境访问海外仓库
3. **更新频率**: 建议根据实际需求设置合适的同步频率
4. **镜像大小**: 大型镜像同步时间较长，请耐心等待

## 维护者

- @alxxxxla

## 许可证

MIT License
