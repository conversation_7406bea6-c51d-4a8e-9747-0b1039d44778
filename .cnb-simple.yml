# 简化版 - 海外Docker镜像同步到CNB
# 使用方法：
# 1. 设置环境变量 token 和 CNB_GROUP_SLUG_LOWERCASE
# 2. 修改下面的镜像列表
# 3. 运行流水线

main:
  push:
  # 启用定时同步（每天凌晨2点）
  # "crontab: 0 2 * * *":
    - runner:
        cpus: 1
        memory: 2048
    - docker:
        image: python:alpine
      imports: https://cnb.cool/${CNB_GROUP_SLUG_LOWERCASE}/key/-/blob/main/key.yml
      stages:
        - name: 安装工具并同步镜像
          script: |
            # 安装必要工具
            apk add --no-cache skopeo curl jq
            pip install requests
            
            # 登录CNB仓库
            echo "登录到 docker.cnb.cool..."
            skopeo login docker.cnb.cool -u cnb -p ${token}
            
            # 定义要同步的镜像列表（在这里修改）
            cat > images.txt << 'EOF'
            # ===========================================
            # 在下面添加需要同步的镜像，每行一个
            # ===========================================
            
            # 常用基础镜像
            nginx:alpine
            redis:7-alpine
            mysql:8.0
            postgres:15-alpine
            
            # 开发工具镜像
            node:18-alpine
            python:3.11-alpine
            golang:1.21-alpine
            
            # 应用镜像示例
            whyour/qinglong:latest
            portainer/portainer-ce
            
            # 更多镜像请添加在这里...
            
            EOF
            
            # 执行同步脚本
            python3 << 'PYTHON_SCRIPT'
            import requests
            import subprocess
            import os
            import sys
            
            token = os.getenv('token')
            group = os.getenv('CNB_GROUP_SLUG_LOWERCASE')
            
            if not token or not group:
                print("❌ 缺少环境变量")
                sys.exit(1)
            
            headers = {
                'accept': 'application/json',
                'Authorization': token,
                'Content-Type': 'application/json'
            }
            
            def create_repo(repo_name):
                data = {
                    'description': f'Docker镜像: {repo_name}',
                    'name': repo_name,
                    'visibility': 'public'
                }
                resp = requests.post(f'https://api.cnb.cool/{group}/-/repos', headers=headers, json=data)
                return resp.status_code in [201, 409]
            
            def sync_image(src, dest):
                try:
                    cmd = ["skopeo", "copy", "--all", f'docker://{src}', f'docker://{dest}']
                    subprocess.run(cmd, check=True, capture_output=True)
                    return True
                except:
                    return False
            
            def parse_image(image):
                parts = image.split('/')
                if len(parts) == 1:
                    repo = parts[0].split(':')[0]
                    dest = f'docker.cnb.cool/{group}/{repo}'
                    if ':' in parts[0]:
                        dest += f":{parts[0].split(':', 1)[1]}"
                elif len(parts) == 2:
                    repo = parts[0]
                    dest = f'docker.cnb.cool/{group}/{parts[0]}/{parts[1]}'
                else:
                    repo = parts[-2] if len(parts) > 2 else parts[-1].split(':')[0]
                    dest = f'docker.cnb.cool/{group}/{repo}/{parts[-1]}'
                return repo, dest
            
            success = 0
            failed = 0
            
            with open('images.txt', 'r') as f:
                for line in f:
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                    
                    print(f"🔄 处理: {line}")
                    repo, dest = parse_image(line)
                    
                    if create_repo(repo):
                        if sync_image(line, dest):
                            print(f"✅ 成功: {dest}")
                            success += 1
                        else:
                            print(f"❌ 失败: {line}")
                            failed += 1
                    else:
                        print(f"❌ 仓库创建失败: {repo}")
                        failed += 1
            
            print(f"\n📊 同步完成: 成功 {success}, 失败 {failed}")
            PYTHON_SCRIPT
