# 海外Docker镜像自动同步到腾讯云CNB平台
# 支持批量同步多个镜像，自动创建仓库，定时检查更新

main:
  push:
  # 定时任务：每天凌晨2点检查更新
  # "crontab: 0 2 * * *":
    - runner:
        cpus: 1
        memory: 2048
    - docker:
        image: python:alpine
      imports: https://cnb.cool/${CNB_GROUP_SLUG_LOWERCASE}/key/-/blob/main/key.yml
      stages:
        # 第一步：安装必要工具
        - name: 安装工具
          script: |
            apk add --no-cache skopeo curl jq
            pip install requests

        # 第二步：登录到CNB仓库
        - name: 登录CNB仓库
          script: |
            echo "登录到 docker.cnb.cool..."
            skopeo login docker.cnb.cool -u cnb -p ${token}

        # 第三步：创建镜像列表文件
        - name: 创建镜像列表
          script: |
            cat > images.txt << 'EOF'
            # 在这里添加需要同步的镜像，每行一个
            # 格式示例：
            whyour/qinglong:latest
            nginx:alpine
            redis:7-alpine
            mysql:8.0
            # 支持多种格式：
            # 1. 简单镜像名：nginx
            # 2. 带标签：nginx:alpine
            # 3. 用户/镜像：whyour/qinglong
            # 4. 完整路径：registry.hub.docker.com/library/nginx:latest
            EOF

        # 第四步：执行同步脚本
        - name: 同步Docker镜像
          script: |
            cat > sync.py << 'EOF'
            import requests
            import subprocess
            import os
            import sys

            # 从环境变量获取认证信息
            token = os.getenv('token')
            group = os.getenv('CNB_GROUP_SLUG_LOWERCASE')

            if not token or not group:
                print("❌ 缺少必要的环境变量: token 或 CNB_GROUP_SLUG_LOWERCASE")
                sys.exit(1)

            # API 请求头配置
            headers = {
                'accept': 'application/json',
                'Authorization': token,
                'Content-Type': 'application/json'
            }

            def check_and_create_repo(repo_name: str):
                """检查仓库是否存在，不存在则创建"""
                request_data = {
                    'description': f'Docker镜像同步仓库: {repo_name}',
                    'license': 'MIT',
                    'name': repo_name,
                    'visibility': 'public'
                }

                print(f"🔍 检查仓库 '{repo_name}' 是否存在...")
                response = requests.post(
                    f'https://api.cnb.cool/{group}/-/repos',
                    headers=headers,
                    json=request_data
                )

                if response.status_code == 201:
                    print(f"✅ 仓库 '{repo_name}' 创建成功")
                    return True
                elif response.status_code == 409:
                    print(f"ℹ️ 仓库 '{repo_name}' 已存在")
                    return True
                else:
                    print(f"❌ 创建仓库失败 (状态码: {response.status_code}): {response.text}")
                    return False

            def copy_docker_image(src: str, dest: str):
                """使用 skopeo 复制 Docker 镜像"""
                try:
                    cmd = ["skopeo", "copy", "--all", f'docker://{src}', f'docker://{dest}']
                    print(f"📦 正在复制镜像: {src} → {dest}")

                    result = subprocess.run(cmd, capture_output=True, text=True, check=True)
                    print("✅ 镜像复制成功！")
                    return True
                except subprocess.CalledProcessError as e:
                    print(f"❌ 复制镜像失败: {e.stderr}")
                    return False

            def parse_image_name(image_line: str):
                """解析镜像名称，返回仓库名和目标地址"""
                # 移除可能的协议前缀
                if image_line.startswith('docker://'):
                    image_line = image_line[9:]

                # 处理不同的镜像格式
                parts = image_line.split('/')

                if len(parts) == 1:
                    # 格式: nginx 或 nginx:latest
                    repo_name = parts[0].split(':')[0]
                    dest = f'docker.cnb.cool/{group}/{repo_name}'
                    if ':' in parts[0]:
                        dest += f":{parts[0].split(':', 1)[1]}"
                    return repo_name, dest

                elif len(parts) == 2:
                    # 格式: whyour/qinglong 或 whyour/qinglong:latest
                    repo_name = parts[0]
                    dest = f'docker.cnb.cool/{group}/{parts[0]}/{parts[1]}'
                    return repo_name, dest

                elif len(parts) >= 3:
                    # 格式: registry.hub.docker.com/library/nginx:latest
                    # 或 docker.io/whyour/qinglong:latest
                    if parts[0] in ['docker.io', 'registry.hub.docker.com']:
                        # Docker Hub 镜像
                        if parts[1] == 'library':
                            # 官方镜像
                            repo_name = parts[2].split(':')[0]
                            dest = f'docker.cnb.cool/{group}/{repo_name}'
                            if ':' in parts[2]:
                                dest += f":{parts[2].split(':', 1)[1]}"
                        else:
                            # 用户镜像
                            repo_name = parts[1]
                            dest = f'docker.cnb.cool/{group}/{parts[1]}/{parts[2]}'
                        return repo_name, dest
                    else:
                        # 其他注册表
                        repo_name = parts[-2] if len(parts) > 2 else parts[-1].split(':')[0]
                        dest = f'docker.cnb.cool/{group}/{repo_name}/{parts[-1]}'
                        return repo_name, dest

                return None, None

            # 主程序
            if __name__ == "__main__":
                success_count = 0
                fail_count = 0

                print(f"🚀 开始同步镜像到 docker.cnb.cool/{group}/")
                print("=" * 50)

                try:
                    with open('images.txt', 'r') as f:
                        for line_num, line in enumerate(f, 1):
                            line = line.strip()

                            # 跳过空行和注释行
                            if not line or line.startswith('#'):
                                continue

                            print(f"\n📋 处理第 {line_num} 行: {line}")

                            repo_name, dest = parse_image_name(line)
                            if not repo_name or not dest:
                                print(f"❌ 无法解析镜像名称: {line}")
                                fail_count += 1
                                continue

                            # 创建仓库
                            if check_and_create_repo(repo_name):
                                # 复制镜像
                                if copy_docker_image(line, dest):
                                    success_count += 1
                                    print(f"🎉 镜像同步成功: {dest}")
                                else:
                                    fail_count += 1
                            else:
                                fail_count += 1

                except FileNotFoundError:
                    print("❌ 找不到 images.txt 文件")
                    sys.exit(1)

                print("\n" + "=" * 50)
                print(f"📊 同步完成统计:")
                print(f"✅ 成功: {success_count} 个镜像")
                print(f"❌ 失败: {fail_count} 个镜像")

                if fail_count > 0:
                    sys.exit(1)
            EOF

            python sync.py