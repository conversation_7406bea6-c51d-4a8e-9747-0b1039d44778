name: docker-image-sync-and-update
description: 海外Docker镜像同步至腾讯云CNB仓库并自动更新
triggers:
  - type: manual  # 手动触发（用于首次拉取新镜像）
  - type: schedule  # 定时触发（每天凌晨2点检测更新）
    cron: "0 2 * * *"

variables:
  - name: SOURCE_IMAGE  # 海外源镜像地址（如whyour/qinglong:latest）
    required: true
  - name: CNB_REPO  # 腾讯云CNB国内仓库地址（如docker.cnb.cool/rakin）
    required: true
  - name: cnb  # 腾讯云CNB仓库用户名
    required: true
  - name: 22o1241OfPSmR5kDt39WfysClfP  # 腾讯云CNB仓库访问令牌
    required: true
  - name: SOURCE_USER  # 海外源仓库用户名（公开镜像可留空）
  - name: SOURCE_PWD  # 海外源仓库密码（公开镜像可留空）

steps:
  # 步骤1：登录仓库（海外源+国内CNB仓库）
  - name: login-registries
    image: docker:latest
    commands:
      - |
        # 登录海外源仓库（如Docker Hub，公开镜像可省略）
        if [ -n "$SOURCE_USER" ] && [ -n "$SOURCE_PWD" ]; then
          docker login -u $SOURCE_USER -p $SOURCE_PWD
        fi
        # 登录腾讯云CNB仓库
        docker login -u $CNB_USER -p $CNB_TOKEN $CNB_REPO

  # 步骤2：检测国内仓库是否已存在目标镜像
  - name: check-local-image
    image: docker:latest
    commands:
      - |
        local_image="$CNB_REPO/$SOURCE_IMAGE"
        # 尝试获取国内镜像元数据（镜像ID+更新时间）
        if docker manifest inspect $local_image > /dev/null 2>&1; then
          local_metadata=$(docker manifest inspect $local_image --verbose | jq -r '.manifests[0].digest, .manifests[0].annotations["org.opencontainers.image.created"]')
          echo "国内镜像元数据: $local_metadata"
        else
          local_metadata=""
          echo "国内仓库无该镜像，准备同步"
        fi
        # 保存国内镜像元数据到环境变量（供后续步骤使用）
        echo "LOCAL_METADATA=$local_metadata" >> $CI_ENV_FILE

  # 步骤3：获取海外源镜像元数据
  - name: get-source-metadata
    image: docker:latest
    commands:
      - |
        # 获取海外源镜像元数据（镜像ID+更新时间）
        source_metadata=$(docker manifest inspect $SOURCE_IMAGE --verbose | jq -r '.manifests[0].digest, .manifests[0].annotations["org.opencontainers.image.created"]')
        echo "海外源镜像元数据: $source_metadata"
        # 保存海外源元数据到环境变量
        echo "SOURCE_METADATA=$source_metadata" >> $CI_ENV_FILE

  # 步骤4：对比版本并同步（首次拉取或版本更新时执行）
  - name: sync-image
    image: docker:latest
    commands:
      - |
        local_image="$CNB_REPO/$SOURCE_IMAGE"
        # 对比国内与海外元数据，不一致则同步
        if [ "$LOCAL_METADATA" != "$SOURCE_METADATA" ]; then
          echo "开始同步镜像: $SOURCE_IMAGE -> $local_image"
          # 拉取海外源镜像
          docker pull $SOURCE_IMAGE
          # 打国内仓库标签
          docker tag $SOURCE_IMAGE $local_image
          # 推送至国内仓库
          docker push $local_image
          echo "同步完成，国内镜像地址: $local_image"
        else
          echo "镜像无更新，无需同步"
        fi